document.addEventListener('DOMContentLoaded', function() {
  // Mobile button handling
  if (window.innerWidth < 750) {
    var sections = document.querySelectorAll('.image-with-text__grid');
    sections.forEach(function(section) {
      var desktopTextBlock = section.querySelector('.image-with-text__text-item--desktop');
      var button = desktopTextBlock ? desktopTextBlock.querySelector('.button') : null;
      var videoBlock = section.querySelector('.image-with-text__media-item');
      if (button && videoBlock) {
        // თუ უკვე არსებობს კლონირებული ღილაკი, აღარ დაამატო
        if (videoBlock.nextSibling && videoBlock.nextSibling.classList && videoBlock.nextSibling.classList.contains('video-with-text-mobile-btn')) {
          button.style.display = 'none';
          return;
        }
        var buttonClone = button.cloneNode(true);
        buttonClone.classList.add('video-with-text-mobile-btn');
        buttonClone.style.margin = '2rem auto 0 auto';
        buttonClone.style.display = 'block';
        buttonClone.style.maxWidth = '350px';
        buttonClone.style.width = '100%';
        buttonClone.style.borderRadius = '8px';
        buttonClone.style.fontSize = '1.15rem';
        buttonClone.style.fontWeight = '600';
        buttonClone.style.letterSpacing = '0.02em';
        buttonClone.style.boxShadow = '0 2px 12px 0 rgba(0,0,0,0.07)';
        buttonClone.style.textAlign = 'center';
        buttonClone.style.padding = '1.1em 0';
        buttonClone.style.lineHeight = '1.2';
        videoBlock.parentNode.insertBefore(buttonClone, videoBlock.nextSibling);
        button.style.display = 'none';
      }
    });
  }

  // Video fullscreen functionality
  function setupVideoFullscreen() {
    var videos = document.querySelectorAll('video');

    videos.forEach(function(video) {
      // Skip if already setup
      if (video.hasAttribute('data-fullscreen-setup')) return;
      video.setAttribute('data-fullscreen-setup', 'true');

      // Add click handler for fullscreen
      video.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Check if we're on mobile (screen width < 750px)
        if (window.innerWidth < 750) {
          enterFullscreen(video);
        }
      });

      // Add touch handler for mobile
      video.addEventListener('touchstart', function(e) {
        if (window.innerWidth < 750) {
          // Add a small delay to distinguish from scroll
          setTimeout(function() {
            if (!video.hasAttribute('data-touch-moved')) {
              enterFullscreen(video);
            }
            video.removeAttribute('data-touch-moved');
          }, 100);
        }
      });

      video.addEventListener('touchmove', function(e) {
        video.setAttribute('data-touch-moved', 'true');
      });
    });
  }

  function enterFullscreen(video) {
    try {
      // Try different fullscreen methods
      if (video.requestFullscreen) {
        video.requestFullscreen().then(function() {
          video.muted = false; // Unmute audio when entering fullscreen
          video.play();
        }).catch(function(err) {
          console.log('Fullscreen request failed:', err);
          fallbackFullscreen(video);
        });
      } else if (video.webkitRequestFullscreen) {
        video.webkitRequestFullscreen();
        video.muted = false; // Unmute audio when entering fullscreen
        video.play();
      } else if (video.mozRequestFullScreen) {
        video.mozRequestFullScreen();
        video.muted = false; // Unmute audio when entering fullscreen
        video.play();
      } else if (video.msRequestFullscreen) {
        video.msRequestFullscreen();
        video.muted = false; // Unmute audio when entering fullscreen
        video.play();
      } else {
        // Fallback for browsers that don't support fullscreen API
        fallbackFullscreen(video);
      }
    } catch (err) {
      console.log('Error entering fullscreen:', err);
      fallbackFullscreen(video);
    }
  }

  function fallbackFullscreen(video) {
    // Create a modal-like fullscreen experience
    var overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.95);
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      box-sizing: border-box;
    `;

    var videoClone = video.cloneNode(true);
    videoClone.style.cssText = `
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
      object-fit: contain;
    `;

    var closeButton = document.createElement('button');
    closeButton.innerHTML = '✕';
    closeButton.style.cssText = `
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.8);
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      font-size: 20px;
      cursor: pointer;
      z-index: 10000;
    `;

    overlay.appendChild(videoClone);
    overlay.appendChild(closeButton);
    document.body.appendChild(overlay);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';

    // Play the cloned video with audio
    videoClone.currentTime = video.currentTime;
    videoClone.muted = false; // Unmute audio for fullscreen video
    videoClone.play();

    // Close handlers
    function closeFullscreen() {
      document.body.removeChild(overlay);
      document.body.style.overflow = '';
      // Sync the original video with the clone
      video.currentTime = videoClone.currentTime;
      if (!videoClone.paused) {
        video.play();
      }
    }

    closeButton.addEventListener('click', closeFullscreen);
    overlay.addEventListener('click', function(e) {
      if (e.target === overlay) {
        closeFullscreen();
      }
    });

    // ESC key handler
    function handleEsc(e) {
      if (e.key === 'Escape') {
        closeFullscreen();
        document.removeEventListener('keydown', handleEsc);
      }
    }
    document.addEventListener('keydown', handleEsc);
  }

  // Initial setup
  setupVideoFullscreen();

  // Re-setup when new content is loaded (for dynamic content)
  var observer = new MutationObserver(function(mutations) {
    var shouldSetup = false;
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === 1) { // Element node
            if (node.tagName === 'VIDEO' || node.querySelector('video')) {
              shouldSetup = true;
            }
          }
        });
      }
    });
    if (shouldSetup) {
      setTimeout(setupVideoFullscreen, 100);
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
});